<#
  Sync every issue in a repo into a GitHub Project (v2)
  Requirements: gh v2.49+ and a token with the “project” scope
#>

param(
    [string]$Owner         = "JortsEnjoyer0",
    [string]$Repo          = "poly-enhanced",
    [string]$ProjectName   = "PolyEnhanced Project",
    [int]   $ProjectNumber = 0,   # set this to 8 if you want to skip name-lookup
    [switch]$DryRun
)

function Get-ProjectNumber {
    param([string]$Owner,[string]$Name)
    $raw = gh project list --owner $Owner --format json |
           ConvertFrom-Json
    $list = if ($raw.projects) { $raw.projects } else { $raw }
    ($list | Where-Object { $_.title -ieq $Name }).number
}

# ── sanity checks ───────────────────────────────────────────
if (-not (Get-Command gh -ErrorAction SilentlyContinue)) {
    throw "GitHub CLI (gh) not in PATH."
}

try { gh auth status | Out-Null }
catch { throw "Run `gh auth login` first – you’re not authenticated." }

if (-not (gh auth status | Select-String 'project')) {
    Write-Host "Adding the 'project' scope to your token …"
    gh auth refresh -s project
}

# ── resolve project number ─────────────────────────────────
if (-not $ProjectNumber) {
    $ProjectNumber = Get-ProjectNumber -Owner $Owner -Name $ProjectName
    if (-not $ProjectNumber) {
        throw "Project '$ProjectName' not found under '$Owner'."
    }
}
Write-Host "Using project #$ProjectNumber ($ProjectName)"

# ── pull URLs already linked so we don’t double-add ─────────
$currentJson  = gh project item-list $ProjectNumber --owner $Owner `
                 --format json --limit 1000 | ConvertFrom-Json
$existingUrls = if ($currentJson.items) { $currentJson.items } else { $currentJson } |
                Where-Object { $_.contentType -eq 'ISSUE' } |
                ForEach-Object { $_.contentUrl }

# ── list every issue in the repo & sync ────────────────────
$issues = gh issue list -R "$Owner/$Repo" --state all --json number,url |
          ConvertFrom-Json

foreach ($issue in $issues) {
    if ($existingUrls -contains $issue.url) {
        Write-Host "✓ Issue #$($issue.number) already linked"
        continue
    }

    $cmd = "gh project item-add $ProjectNumber --owner $Owner --url $($issue.url)"
    if ($DryRun) {
        Write-Host "[DRY-RUN] $cmd"
    } else {
        Write-Host "→ Linking issue #$($issue.number)…"
        iex $cmd
        if ($LASTEXITCODE) { Write-Warning "  Failed (exit $LASTEXITCODE)" }
    }
}
