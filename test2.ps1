<#
.SYNOPSIS
    Add every issue in a repository to a specific GitHub Project (v2).

.PARAMETER Owner
    Account that owns the project/repo (user or org).  
    Default: "JortsEnjoyer0"

.PARAMETER Repo
    The short repository name.  
    Default: "poly-enhanced"

.PARAMETER ProjectName
    Exact-case title of the Project you want to sync to.  
    Default: "PolyEnhanced Project"

.PARAMETER DryRun
    If supplied, only prints the commands instead of executing them.

.EXAMPLE
    .\Sync-IssuesToProject.ps1 -DryRun
#>

param(
    [string]$Owner       = "JortsEnjoyer0",
    [string]$Repo        = "poly-enhanced",
    [string]$ProjectName = "PolyEnhanced Project",
    [switch]$DryRun
)

# ── sanity checks ──────────────────────────────────────────────────────────────
if (-not (Get-Command gh -ErrorAction SilentlyContinue)) {
    Write-Error "GitHub CLI (gh) isn’t installed or not in PATH."; exit 1
}

try { gh auth status | Out-Null }
catch { Write-Error "You aren’t logged in. Run  gh auth login  first."; exit 1 }

# Ensure the token has the project scope (interactive refresh if missing)
if (-not (gh auth status | Select-String "project")) {
    Write-Host "Adding the project scope to your token…"
    gh auth refresh -s project
}

# ── locate the project number by its name ─────────────────────────────────────
$projects = gh project list --owner $Owner --format json | ConvertFrom-Json
$project  = $projects | Where-Object { $_.title -eq $ProjectName }

if (-not $project) {
    Write-Error "Can’t find a project called “$ProjectName” under $Owner."; exit 1
}

$projectNumber = $project.number
Write-Host "Project ‹$ProjectName› found → #$projectNumber"

# ── grab any issues already in the project so we don’t double-add ─────────────
$currentItems = gh project item-list $projectNumber --owner $Owner --format json --limit 1000 `
                 | ConvertFrom-Json
$existingUrls  = $currentItems |
                 Where-Object { $_.contentType -eq "ISSUE" } |
                 ForEach-Object { $_.contentUrl }

# ── pull every issue from the repo ────────────────────────────────────────────
$issues = gh issue list -R "$Owner/$Repo" --state all --json number,url |
          ConvertFrom-Json

foreach ($issue in $issues) {
    if ($existingUrls -contains $issue.url) {
        Write-Host "• Issue #$($issue.number) already linked → skipping"
        continue
    }

    $cmd = "gh project item-add $projectNumber --owner $Owner --url $($issue.url)"
    if ($DryRun) {
        Write-Host "[DRY-RUN] $cmd"
    }
    else {
        Write-Host "➕ Linking issue #$($issue.number)…"
        & $cmd
        if ($LASTEXITCODE) { Write-Warning "  ↳ failed (exit $LASTEXITCODE)" }
    }
}
